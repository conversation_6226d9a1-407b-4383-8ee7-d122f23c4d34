import React, { useState, useMemo } from 'react';
import AssignSkillModal from './assignSkillModal';
import { Input } from '@/components/common/input';
import {
  getSkillLevelColor,
  getSkillLevelName,
} from '@/utils/skillLevelColors';
import SkillLevelLegend from '@/components/skills/skillLevelLegend';
import { Dialog, DialogTrigger } from '@/components/common/dialog';

// Type definitions
interface Employee {
  id: string;
  name: string;
}

interface Skill {
  id: string;
  name: string;
}

interface SkillCategory {
  category: string;
  skills: Skill[];
}

interface SkillLevel {
  employeeId: string;
  skillId: string;
  level: string;
}

interface SkillRecord {
  id: string;
  skill_id: string;
  skill_name: string;
  employee_id: string;
  employee_name: string;
  category_id: string;
  category_name: string;
  current_skill_level_id: string;
  current_skill_level_name: string;
}

const transformSkillsData = (data?: { records?: SkillRecord[] }) => {
  const employees = new Map<string, Employee>();
  const skillCategories = new Map<
    string,
    { category: string; skills: Map<string, Skill> }
  >();
  const skillLevels: SkillLevel[] = [];

  (data?.records ?? []).forEach((record) => {
    employees.set(record.employee_id, {
      id: record.employee_id,
      name: record.employee_name,
    });

    if (!skillCategories.has(record.category_name)) {
      skillCategories.set(record.category_name, {
        category: record.category_name,
        skills: new Map<string, Skill>(),
      });
    }

    const category = skillCategories.get(record.category_name)!;
    category.skills.set(record.skill_id, {
      id: record.skill_id,
      name: record.skill_name,
    });

    skillLevels.push({
      employeeId: record.employee_id,
      skillId: record.skill_id,
      level: record.current_skill_level_name,
    });
  });

  return {
    employees: Array.from(employees.values()),
    skillCategories: Array.from(skillCategories.values()).map((cat) => ({
      category: cat.category,
      skills: Array.from(cat.skills.values()),
    })),
    skillLevels,
  };
};

export default function SkillsMatrix({
  skillsData,
  skillCategories,
  skillLevels = [], // Provide default empty array
  employeesData,
  reFetch,
}: {
  skillsData: any;
  skillCategories: any[];
  skillLevels: any[];
  employeesData: any[];
  reFetch: () => void;
}) {
  const {
    employees: mockEmployees,
    skillCategories: mockSkillCategories,
    skillLevels: mockSkillLevels,
  } = transformSkillsData(skillsData ?? { records: [] });

  const [selected, setSelected] = useState<{
    employeeId: string;
    skillId: string;
    employeeName?: string;
    skillName?: string;
  } | null>(null);
  const [search, setSearch] = useState('');

  const getLevel = (employeeId: string, skillId: string) => {
    const found = mockSkillLevels.find(
      (l) => l.employeeId === employeeId && l.skillId === skillId,
    );
    return found ? found.level : '+';
  };

  const getLevelDisplay = (level: string) => {
    if (level === '+') return { level: '+', name: 'Not Assigned' };
    // Keep the "L" prefix for display
    return { level, name: getSkillLevelName(level.replace('L', '')) };
  };

  const getLevelStyles = (level: string) => {
    if (level === '+') {
      return 'bg-white-100 text-grey-300 border border-dashed border-grey-200';
    }
    const levelNumber = level.replace('L', '');
    const colors = getSkillLevelColor(levelNumber);
    return `${colors.bg} text-white font-medium`;
  };

  const filteredEmployees = useMemo((): Employee[] => {
    return (mockEmployees || []).filter((e) =>
      e.name.toLowerCase().includes(search.toLowerCase()),
    );
  }, [mockEmployees, search]);

  const filteredSkillCategories = useMemo((): SkillCategory[] => {
    if (!search.trim()) return mockSkillCategories || [];
    return (mockSkillCategories || []).map((cat) => ({
      ...cat,
      skills: (cat.skills || []).filter((s: Skill) =>
        s.name.toLowerCase().includes(search.toLowerCase()),
      ),
    }));
  }, [mockSkillCategories, search]);

  return (
    <div className="space-y-5">
      <div className="mt-4">
        <SkillLevelLegend skillLevels={skillLevels || []} />
      </div>
      <div className="flex flex-col sm:flex-row justify-between mb-4 gap-2 mt-4">
        <div className="flex items-center justify-between relative w-full">
          <Input
            placeholder="Search by employee or skill"
            className="w-[40vw] bg-white flex-auto rounded-lg border border-grey-100 py-2.5 px-3 text-black outline-none transition focus:border-primary active:border-primary"
            onChange={(e) => {
              setSearch(e.target.value);
            }}
            value={search}
          />
        </div>
      </div>

      {/* Desktop/Tablet Table */}
      <div className="hidden md:block rounded-lg border border-white-200 bg-white-100 shadow-sm max-h-[70vh] overflow-auto">
        <table className="min-w-full text-base border-collapse table-auto">
          <thead>
            {/* Header row: sticky at top */}
            <tr className="bg-white-200 text-left border border-grey-100 sticky top-0 z-40">
              {/* Skills column header (auto width) */}
              <th className="px-3 py-3 sticky left-0 z-50 bg-white-200">
                <span className="text-base font-semibold text-grey-300 truncate">
                  Skills
                </span>
              </th>
              {/* Employee columns */}
              {filteredEmployees.length === 0 ? (
                <th className="px-3 py-3 bg-white-200 border-b border-grey-100 min-w-[120px]">
                  <span className="text-base font-semibold text-grey-300 truncate">
                    No employees found
                  </span>
                </th>
              ) : (
                filteredEmployees.map((emp) => (
                  <th
                    key={emp.id}
                    className="px-3 py-3 bg-white-200 border-b border-grey-100 min-w-[120px]"
                    title={emp.name}
                  >
                    <span className="text-base font-semibold text-grey-300 truncate block max-w-[10rem] lg:max-w-[14rem]">
                      {emp.name}
                    </span>
                  </th>
                ))
              )}
            </tr>
          </thead>

          <tbody>
            {filteredSkillCategories.length === 0 ? (
              <tr>
                <td
                  colSpan={filteredEmployees.length + 1}
                  className="p-4 text-center text-gray-400"
                >
                  No skills available
                </td>
              </tr>
            ) : (
              filteredSkillCategories.map((cat) => (
                <React.Fragment key={cat.category}>
                  {/* Sticky Category Row */}
                  <tr className="border-t border-white-200">
                    {/* Sticky first column */}
                    <td
                      className="px-3 py-2 text-dark-300 font-semibold sticky left-0 z-40 bg-white-50"
                      style={{ top: '48px' }}
                    >
                      {cat.category}
                    </td>
                    <td
                      colSpan={filteredEmployees.length}
                      className="bg-white-50"
                      style={{ top: '48px' }}
                    />
                  </tr>

                  {/* Skills rows */}
                  {(cat.skills || []).length === 0 ? (
                    <tr>
                      <td
                        colSpan={filteredEmployees.length + 1}
                        className="p-4 text-center text-gray-400"
                      >
                        No skills in this category
                      </td>
                    </tr>
                  ) : (
                    (cat.skills || []).map((skill: Skill) => (
                      <tr key={skill.id} className="border-t border-white-200">
                        {/* Sticky skill name */}
                        <td className="p-2 sticky left-0 z-30 bg-white-100">
                          <span className="inline-block rounded-full bg-primary-100 text-primary-400 px-3 py-1 text-sm font-medium whitespace-nowrap">
                            {skill.name}
                          </span>
                        </td>

                        {/* Employee skill levels */}
                        {filteredEmployees.map((emp) => {
                          const level = getLevel(emp.id, skill.id);
                          const levelDisplay = getLevelDisplay(level);
                          const levelStyles = getLevelStyles(level);
                          return (
                            <td
                              key={emp.id}
                              className="p-2 cursor-pointer hover:bg-white-50"
                              onClick={() =>
                                setSelected({
                                  employeeId: emp.id,
                                  skillId: skill.id,
                                  employeeName: emp.name,
                                  skillName: skill.name,
                                })
                              }
                              title={`${emp.name} - ${levelDisplay.name}`}
                            >
                              <span
                                className={`px-2 py-1 rounded-full text-xs ${levelStyles}`}
                              >
                                {levelDisplay.level}
                              </span>
                            </td>
                          );
                        })}
                      </tr>
                    ))
                  )}
                </React.Fragment>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Mobile List */}
      <div className="md:hidden space-y-3">
        {filteredSkillCategories.map((cat) => (
          <div
            key={cat.category}
            className="rounded-lg border border-white-200 bg-white-100 shadow-sm"
          >
            <div className="px-3 py-2 bg-white-50 text-dark-300 font-semibold rounded-t-lg sticky top-0 z-10">
              {cat.category}
            </div>
            <div className="divide-y divide-white-200">
              {(cat.skills || []).map((skill: Skill) => (
                <div key={skill.id} className="p-3 space-y-2">
                  <div>
                    <span className="inline-block rounded-full bg-primary-100 text-primary-400 px-3 py-1 text-xs font-medium">
                      {skill.name}
                    </span>
                  </div>
                  <div className="flex gap-2 overflow-x-auto pb-1">
                    {filteredEmployees.map((emp) => {
                      const level = getLevel(emp.id, skill.id);
                      const levelDisplay = getLevelDisplay(level);
                      const levelStyles = getLevelStyles(level);
                      return (
                        <button
                          key={emp.id}
                          className="flex-shrink-0 inline-flex items-center gap-2 px-3 py-2 rounded-lg border border-white-200 bg-white-100 hover:bg-white-50"
                          onClick={() =>
                            setSelected({
                              employeeId: emp.id,
                              skillId: skill.id,
                              employeeName: emp.name,
                              skillName: skill.name,
                            })
                          }
                          title={`${emp.name} - ${levelDisplay.name}`}
                        >
                          <span className="max-w-[8rem] truncate text-sm text-dark-200">
                            {emp.name}
                          </span>
                          <span
                            className={`px-2 py-0.5 rounded-full text-sm ${levelStyles}`}
                          >
                            {levelDisplay.level}
                          </span>
                        </button>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {selected && (
        <Dialog open={!!selected} onOpenChange={() => setSelected(null)}>
          <AssignSkillModal
            employeeId={selected.employeeId}
            skillId={selected.skillId}
            employeeName={selected.employeeName}
            skillName={selected.skillName}
            onClose={() => setSelected(null)}
            skillLevels={skillLevels || []}
            skillsData={skillsData}
            reFetch={reFetch}
            open
          />
        </Dialog>
      )}
    </div>
  );
}
